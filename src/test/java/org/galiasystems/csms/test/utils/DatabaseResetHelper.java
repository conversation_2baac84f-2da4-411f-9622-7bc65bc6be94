package org.galiasystems.csms.test.utils;

import io.quarkus.arc.Arc;
import io.quarkus.test.junit.callback.QuarkusTestAfterEachCallback;
import io.quarkus.test.junit.callback.QuarkusTestMethodContext;
import org.galiasystems.csms.test.services.DatabaseResetService;

import java.util.logging.Logger;

/**
 * Helper utility for performing database resets in Quarkus tests.
 * This class implements QuarkusTestAfterEachCallback to ensure it runs
 * within the Quarkus test context where CDI is available.
 */
public class DatabaseResetHelper implements QuarkusTestAfterEachCallback {

    private static final Logger logger = Logger.getLogger(DatabaseResetHelper.class.getName());

    @Override
    public void afterEach(QuarkusTestMethodContext context) {
        // Check if a database reset was requested for this test
        var resetRequested = context.getTestExtensionContext()
                                   .getStore(context.getTestExtensionContext().getNamespace().create("DatabaseReset"))
                                   .get("resetRequested", Boolean.class);
        
        if (Boolean.TRUE.equals(resetRequested)) {
            var testMethodName = context.getTestExtensionContext()
                                       .getStore(context.getTestExtensionContext().getNamespace().create("DatabaseReset"))
                                       .get("testMethodName", String.class);
            
            logger.info("Performing database reset after test: " + testMethodName);
            
            try {
                // Get the DatabaseResetService from the CDI container
                var container = Arc.container();
                if (container != null) {
                    var serviceInstance = container.instance(DatabaseResetService.class);
                    if (serviceInstance.isAvailable()) {
                        DatabaseResetService resetService = serviceInstance.get();
                        
                        // Execute the database reset synchronously
                        resetService.resetDatabase()
                            .await()
                            .indefinitely();
                        
                        logger.info("Database reset completed for test: " + testMethodName);
                    } else {
                        logger.warning("DatabaseResetService not available in CDI container");
                    }
                } else {
                    logger.warning("CDI container not available");
                }
                
            } catch (Exception e) {
                logger.severe("Failed to reset database after test " + testMethodName + ": " + e.getMessage());
                e.printStackTrace();
                // Don't throw the exception to avoid failing the test
            } finally {
                // Clear the reset request
                context.getTestExtensionContext()
                       .getStore(context.getTestExtensionContext().getNamespace().create("DatabaseReset"))
                       .remove("resetRequested");
                context.getTestExtensionContext()
                       .getStore(context.getTestExtensionContext().getNamespace().create("DatabaseReset"))
                       .remove("testMethodName");
            }
        }
    }
}
