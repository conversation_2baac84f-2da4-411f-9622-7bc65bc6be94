package org.galiasystems.csms.test.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.junit.jupiter.api.extension.ExtendWith;
import org.galiasystems.csms.test.extensions.ResetAfterTestExtension;

/**
 * Annotation to mark test methods that should have the database reset after execution.
 * 
 * When applied to a test method, this annotation will:
 * 1. Delete the entire database after the test completes
 * 2. Run all Flyway migration scripts from resources/db/migration
 * 3. Execute the import.sql script from resources/db/data/import.sql
 * 
 * This ensures a clean database state for subsequent tests.
 * 
 * Usage:
 * <pre>
 * {@code
 * @Test
 * @ResetAfterTest
 * public void testThatModifiesDatabase() {
 *     // Test code that modifies database
 * }
 * }
 * </pre>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@ExtendWith(ResetAfterTestExtension.class)
public @interface ResetAfterTest {
}
