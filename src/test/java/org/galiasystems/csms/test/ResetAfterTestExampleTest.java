package org.galiasystems.csms.test;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.galiasystems.csms.management.repository.ChargingStationRepository;
import org.galiasystems.csms.test.annotations.ResetAfterTest;
import org.galiasystems.csms.test.utils.DatabaseResetUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Example test class demonstrating the usage of @ResetAfterTest annotation.
 * This test shows how to use the annotation to reset the database after test execution.
 */
@QuarkusTest
public class ResetAfterTestExampleTest {

    @Inject
    ChargingStationRepository chargingStationRepository;

    @Test
    @DisplayName("Example test that modifies database and resets after")
    @ResetAfterTest
    public void testThatModifiesDatabaseWithReset() {
        // This test would modify the database
        // After this test completes, the database will be reset to initial state

        // Verify that the repository is available
        assertNotNull(chargingStationRepository);

        // In a real test, you would:
        // 1. Modify database data
        // 2. Verify the modifications
        // 3. The @ResetAfterTest annotation ensures database is reset after this test
    }

    @Test
    @DisplayName("Example test without database reset")
    public void testWithoutDatabaseReset() {
        // This test does not have @ResetAfterTest annotation
        // Database will not be reset after this test

        assertNotNull(chargingStationRepository);
    }

    @Test
    @DisplayName("Example test using manual database reset (recommended)")
    public void testWithManualDatabaseReset() {
        try {
            // Verify that the repository is available
            assertNotNull(chargingStationRepository);

            // In a real test, you would:
            // 1. Modify database data
            // 2. Verify the modifications

        } finally {
            // Manually reset database after test
            DatabaseResetUtil.resetDatabase();
        }
    }
}
