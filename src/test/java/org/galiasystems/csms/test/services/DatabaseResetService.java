package org.galiasystems.csms.test.services;

import io.quarkus.arc.profile.IfBuildProfile;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.flywaydb.core.Flyway;
import org.hibernate.reactive.mutiny.Mutiny;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.logging.Logger;

/**
 * Service responsible for resetting the database during tests.
 * This service is only available in the test profile.
 */
@ApplicationScoped
@IfBuildProfile("test")
public class DatabaseResetService {

    private static final Logger logger = Logger.getLogger(DatabaseResetService.class.getName());

    @Inject
    Mutiny.SessionFactory sessionFactory;

    @Inject
    Flyway flyway;

    /**
     * Resets the database by:
     * 1. Cleaning the database (dropping all objects)
     * 2. Running Flyway migrations
     * 3. Executing the import.sql script
     * 
     * @return Uni<Void> that completes when the reset is finished
     */
    public Uni<Void> resetDatabase() {
        logger.info("Starting database reset...");
        
        return Uni.createFrom().item(() -> {
            try {
                // Clean the database using Flyway
                logger.info("Cleaning database...");
                flyway.clean();
                
                // Run migrations
                logger.info("Running Flyway migrations...");
                flyway.migrate();
                
                logger.info("Database reset completed successfully");
                return null;
            } catch (Exception e) {
                logger.severe("Failed to reset database: " + e.getMessage());
                throw new RuntimeException("Database reset failed", e);
            }
        }).chain(ignored -> {
            // Execute import.sql script
            return executeImportScript();
        });
    }

    /**
     * Executes the import.sql script to populate initial test data.
     * 
     * @return Uni<Void> that completes when the script execution is finished
     */
    private Uni<Void> executeImportScript() {
        logger.info("Executing import.sql script...");
        
        return sessionFactory.withSession(session -> {
            try {
                // Read the import.sql file
                Path importSqlPath = Paths.get("src/main/resources/db/data/import.sql");
                if (!Files.exists(importSqlPath)) {
                    logger.warning("import.sql file not found at: " + importSqlPath);
                    return Uni.createFrom().voidItem();
                }
                
                String importSql = Files.readString(importSqlPath);
                
                // Split by semicolon and execute each statement
                String[] statements = importSql.split(";");
                
                Uni<Void> chain = Uni.createFrom().voidItem();
                
                for (String statement : statements) {
                    String trimmedStatement = statement.trim();
                    if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                        chain = chain.chain(ignored -> 
                            session.createNativeQuery(trimmedStatement)
                                   .executeUpdate()
                                   .replaceWith(Uni.createFrom().voidItem())
                        );
                    }
                }
                
                return chain;
                
            } catch (IOException e) {
                logger.severe("Failed to read import.sql: " + e.getMessage());
                return Uni.createFrom().failure(new RuntimeException("Failed to execute import script", e));
            }
        });
    }
}
